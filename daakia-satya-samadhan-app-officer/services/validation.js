import { AUTH_ERRORS } from '../constants/auth';

export const STATUS = {
  INITIATED: "initiated",
  DISPATCHED: "dispatched",
  RECEIVED: "received",
  REJECTED: "rejected",
  COMPLETED: "completed",
};

export const validationService = {
  validateMobile: (mobileNumber) => {
    if (!mobileNumber || mobileNumber.length !== 10) {
      throw new Error(AUTH_ERRORS.INVALID_MOBILE);
    }
  },

  validateOtp: (otp) => {
    if (otp.length !== 5) {
      throw new Error(AUTH_ERRORS.INVALID_OTP);
    }
  },

  validateEmail: (email) => {
    if (email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      throw new Error(AUTH_ERRORS.INVALID_EMAIL);
    }
  },

  validateName: (name) => {
    if (name && name.trim().length < 2) {
      throw new Error(AUTH_ERRORS.INVALID_NAME);
    }
  },

  validateAadhar: (aadhar) => {
    if (!aadhar || aadhar.length !== 12 || isNaN(aadhar)) {
      throw new Error(AUTH_ERRORS.INVALID_AADHAR);
    }
  },

  validateProfileUpdate: (updateData) => {
    const allowedFields = ['name', 'emailId', 'mobileNumber', 'displayUrl'];
    const filteredData = {};
    
    allowedFields.forEach(field => {
      if (updateData[field] !== undefined) {
        filteredData[field] = updateData[field];
      }
    });

    if (filteredData.emailId) {
      validationService.validateEmail(filteredData.emailId);
    }

    if (filteredData.mobileNumber) {
      validationService.validateMobile(filteredData.mobileNumber);
    }

    if (filteredData.name) {
      validationService.validateName(filteredData.name);
    }

    return filteredData;
  },

  validateRegistration: (data) => {
    const { name, mobileNumber, aadhar, emailId } = data;
    
    validationService.validateName(name);
    validationService.validateMobile(mobileNumber);
    validationService.validateAadhar(aadhar);
    validationService.validateEmail(emailId);

    return data;
  },
}; 